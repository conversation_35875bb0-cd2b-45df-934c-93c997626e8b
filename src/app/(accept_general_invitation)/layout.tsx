import React from "react";
import Header from "~/telexComponents/header";
import ExternalPageFooter from "~/telexComponents/externalPageFooter";
import { Toaster } from "~/components/ui/toaster";

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <>
      <Header />
      <div className="pt-12">{children}</div>
      <ExternalPageFooter />
      <Toaster />
    </>
  );
}
