"use client";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { ChevronDown, ChevronDownIcon } from "lucide-react";
import { DataContext } from "~/store/GlobalState";
import { useContext } from "react";
import Image from "next/image";
import { ACTIONS } from "~/store/Actions";
import { getInitials } from "~/utils/utils";

export default function OrganisationMenu() {
  const { state, dispatch } = useContext(DataContext);
  const { orgData } = state;

  const handleLogout = async () => {
    localStorage.clear();
    window.location.href = "/auth/login";
  };

  //

  return (
    <div className="">
      <Popover>
        <PopoverTrigger asChild>
          <div className="flex items-center gap-1 cursor-pointer">
            <h6 className="text-lg leading-[26px] font-semibold text-white">
              {orgData?.name}
            </h6>
            <ChevronDownIcon className="text-white mt-1" />
          </div>
        </PopoverTrigger>

        <PopoverContent
          align="start"
          className="w-[270px] p-0 rounded-md shadow-xl"
        >
          <div className="">
            <div className="flex items-center gap-3 p-3 border-b font-medium text-sm">
              <div className="size-9 rounded border overflow-hidden flex items-center justify-center">
                {orgData?.logo_url ? (
                  <Image
                    src={orgData?.logo_url}
                    alt=""
                    width={50}
                    height={50}
                    unoptimized
                    className="size-9"
                  />
                ) : (
                  <h3 className="text-primary-500 font-bold text-sm">
                    {getInitials(orgData?.name)}
                  </h3>
                )}
              </div>

              <div>
                <div className="text-sm">{orgData?.name}</div>
                <div
                  className={`text-muted-foreground text-xs ${orgData?.email === "" ? "invisible" : ""}`}
                >
                  {orgData?.email}
                </div>
              </div>
            </div>

            <div className="text-xs px-3 py-3 font-medium border-b hover:bg-blue-500 hover:text-white cursor-pointer">
              🚀 Your <strong>Pro trial</strong> lasts through{" "}
              <strong>June 13</strong>.
              <br />
              <a href="#" className="text-blue-600 block hover:underline">
                See upgrade options
              </a>
            </div>
          </div>

          <ul className="text-sm pb-3">
            <li
              onClick={() =>
                dispatch({ type: ACTIONS.INVITE_MODAL, payload: true })
              }
              className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2"
            >
              Invite people to {orgData?.name}
            </li>
            <li className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between">
              Preferences
            </li>
            <li className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between">
              Tools & settings <ChevronDown className="h-4 w-4" />
            </li>
            <li className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex items-center gap-2">
              Sign in on mobile
            </li>
            <li
              onClick={handleLogout}
              className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 font-medium"
            >
              Sign out
            </li>
            <li className="hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex items-center justify-between gap-2">
              Open the Telex App
              <Image
                src="/images/telex-logo.svg"
                alt=""
                width={40}
                height={40}
                unoptimized
                className="size-5"
              />
            </li>
          </ul>
        </PopoverContent>
      </Popover>
    </div>
  );
}
