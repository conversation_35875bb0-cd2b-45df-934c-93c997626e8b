import StorageIndicator from "~/app/(client)/client/_components/storage/storage-indicator";
import Icons from "~/app/(client)/client/_components/billing/icons";
import { CircleHelp } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";

const BilllingAICreditBalance = ({
  creditUsed,
  creditUnit,
  totalCredit,
}: {
  totalCredit: number;
  creditUsed: number;
  creditUnit: string;
}) => {
  const creditPercentage = (creditUsed / totalCredit) * 100;

  return (
    <div className="bg-white border rounded-xl h-full overflow-hidden ">
      <div className="space-y-4 ">
        <div className="bg-[#F9FAFB] p-6 border-b">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2 cursor-pointer">
              <Icons name="money" svgProps={{}} />
              <p className="text-[#667085]">Balance</p>
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <div className="bg-white p-1 rounded-full border cursor-pointer">
                  <CircleHelp size={16} color="#667085" />
                </div>
              </PopoverTrigger>
              <PopoverContent
                className="w-80 relative -mr-[2px] -mt-[80px]"
                side="top"
                align="end"
              >
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    AI Credits are shared by your org and are consumed whenever
                    agents perform tasks.
                  </p>
                </div>
                <div className="absolute bottom-0 -right-[0px] transform -translate-x-1/2 translate-y-[70%] w-0 h-0 border-l-8 border-l-transparent border-r-8 border-r-transparent border-t-8 border-popover"></div>
              </PopoverContent>
            </Popover>
          </div>
          <div className="w-full mt-4">
            <div className="flex items-center gap-2 text-[#475467]">
              <p className="font-medium text-2xl">
                {" "}
                {new Intl.NumberFormat("en-US").format(
                  totalCredit - creditUsed
                )}
              </p>
              <span className="text-sm">AI Credits</span>
            </div>
          </div>
        </div>

        <div className="px-6 pb-6 pt-2 ">
          <StorageIndicator storagePercentage={creditPercentage} />

          <p className="text-sm text-gray-900 mt-2">
            {new Intl.NumberFormat("en-US").format(creditUsed)} {creditUnit}{" "}
            <span className="text-gray-500">Used Today</span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default BilllingAICreditBalance;
