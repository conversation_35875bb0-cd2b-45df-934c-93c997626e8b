import React from "react";
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  ColumnDef,
  flexRender,
  SortingState,
  ColumnFiltersState,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import EmptyState from "./billing-ai-credit-empty-usage-table";
import { Button } from "~/components/ui/button";
import Icons from "~/app/(client)/client/_components/billing/icons";
import { cn } from "~/lib/utils";
import { TelexTransaction } from "../type";
import { mockData, SampleBillingPaymentHistory } from "../data";
import Image from "next/image";
import images from "~/assets/images";

export default function BillingPaymentHistoryTable() {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );

  const columns: ColumnDef<TelexTransaction>[] = [
    {
      accessorKey: "description",
      header: () => {
        return <p className="text-[#667085]">Description</p>;
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col items-start gap-0">
            <span className="text-gray-700">{row.original.description}</span>
            <span className="text-[#667085] text-sm">
              {row.original.extraDescription}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "date",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Date
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => (
        <span className="text-gray-700">{row.getValue("date")}</span>
      ),
    },
    {
      accessorKey: "amount.total",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Amount
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="flex flex-col items-start gap-0">
          <span className="text-gray-700">${row.original.amount.total}</span>
          <span className="text-[#667085] text-sm">{`$${row.original.amount.base} + ($${row.original.amount.vat} VAT)`}</span>
        </div>
      ),
    },
    {
      accessorKey: "paymentMethod.lastFour",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Payment Method
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <Image
            src={images.visaLogo}
            alt={""}
            width={50}
            height={50}
            className={cn(`border rounded bg-white border-[#F5F5F5] py-2 px-2`)}
          />
          <div className="flex items-center gap-1">
            <span>••••</span>
            <span className="text-gray-700">
              ${row.original.paymentMethod.lastFour}
            </span>
            <span className="text-[#667085] text-sm">{`(${row.original.paymentMethod.processor})`}</span>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "action",
      header: () => {
        return (
          <div className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]">
            Actions
          </div>
        );
      },
      cell: ({ row }) => (
        <Button
          onClick={() =>
            window.open(`/billing/invoice/${row.original.id}`, "_blank")
          }
          variant={"outline"}
          className="text-gray-700 h-fit p-1"
        >
          <Image src={images.fileDoc} alt={""} width={25} height={25} />
        </Button>
      ),
    },
  ];

  const tableData = SampleBillingPaymentHistory;

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    state: {
      sorting,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },

    autoResetPageIndex: false,
  });

  const currentPage = table.getState().pagination.pageIndex + 1;
  const totalPages = table.getPageCount();
  const hasData = mockData.length > 0;

  return (
    <div className="w-full mx-auto  bg-white border  mt-6 rounded-xl overflow-hidden">
      {hasData ? (
        <>
          <div className="border-b">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="bg-gray-50">
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className="font-medium text-gray-700"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-gray-50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="py-4">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between px-6 space-x-2 py-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="flex items-center gap-2 border-gray-300"
            >
              <Icons name="move-left" svgProps={{}} />
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                let pageNum: number;
                if (totalPages <= 7) {
                  pageNum = i + 1;
                } else if (currentPage <= 4) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 3) {
                  pageNum = totalPages - 6 + i;
                } else {
                  pageNum = currentPage - 3 + i;
                }

                if (pageNum === 4 && currentPage > 4 && totalPages > 7) {
                  return (
                    <span key="ellipsis1" className="px-2">
                      ...
                    </span>
                  );
                }
                if (
                  pageNum === totalPages - 3 &&
                  currentPage < totalPages - 3 &&
                  totalPages > 7
                ) {
                  return (
                    <span key="ellipsis2" className="px-2">
                      ...
                    </span>
                  );
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => table.setPageIndex(pageNum - 1)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="flex items-center gap-2 border-gray-300"
            >
              Next
              <Icons name="move-right" svgProps={{}} />
            </Button>
          </div>
        </>
      ) : (
        <EmptyState />
      )}
    </div>
  );
}
