"use client";
import React, { useState } from "react";
import SettingsLabel from "../../../components/settings-label";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb";
import { pricingTiers } from "../data";
import { Check, Loader2 } from "lucide-react";
import { Switch } from "~/components/ui/switch";
import Icons from "~/app/(client)/client/_components/billing/icons";
import { Button } from "~/components/ui/button";
import cogoToast from "cogo-toast";
import StorageOffloadModal from "../../../../_components/storage/storage-offload-modal";
import BillingCancellationModal from "../components/billing-cancellation-modal";

const Page = () => {
  const [currentPlan, setCurrentPlan] = useState<string>(() => {
    const current = pricingTiers.find((tier) => tier.isCurrentPlan);
    return current?.name || "";
  });
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const [isOffloadModalOpen, setIsOffloadModalOpen] = useState(false);
  const [isBillingCancellationModalOpen, setIsBillingCancellationModalOpen] =
    useState(false);
  const [
    alternateStorageCancellationModalOpen,
    setAlternateStorageCancellationModalOpen,
  ] = useState(false);

  const PLAN_HIERACHRY = ["Free", "Starter", "Community"];

  const handleUpgrade = async (planName: string) => {
    setLoadingPlan(planName);

    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const isSuccess = Math.random() > 0.3;

      if (isSuccess) {
        setCurrentPlan(planName);
        cogoToast.success(`Successfully upgraded to ${planName} plan!`, {
          position: "top-right",
          hideAfter: 4,
        });
      } else {
        cogoToast.error("Upgrade failed. Please try again later.", {
          position: "top-right",
          hideAfter: 4,
        });
      }
    } catch (error) {
      cogoToast.error("An error occurred during upgrade. Please try again.", {
        position: "top-right",
        hideAfter: 4,
      });
    } finally {
      setLoadingPlan(null);
    }
  };

  return (
    <div>
      <BillingCancellationModal
        isOpen={isBillingCancellationModalOpen}
        onClose={() => setIsBillingCancellationModalOpen(false)}
        onCancel={() => {
          handleUpgrade(currentPlan);
          setIsBillingCancellationModalOpen(false);
        }}
      />
      <StorageOffloadModal
        isOpen={isOffloadModalOpen}
        onClose={() => setIsOffloadModalOpen(false)}
      />
      <SettingsLabel />
      <div className="p-5 border-b">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/client/settings/organisation/billing">
                Billing
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-black">All Plans</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className="w-full flex items-center justify-center mt-12">
        <div className="flex items-center gap-3">
          <p className="text-sm font-bold">Pay monthly</p>
          <Switch id="airplane-mode" />
          <div className="relative">
            <p className="text-sm font-bold text-[#667085]">Pay annually</p>
            <span className="absolute -top-6 -right-[110%]">
              <Icons name="billing-label" svgProps={{}} />
            </span>
          </div>
        </div>
      </div>
      <div className="flex flex-col lg:flex-row gap-6 px-6 py-3 mx-auto">
        {pricingTiers.map((tier, index) => {
          const isCurrentPlan = tier.name === currentPlan;
          const isLoading = loadingPlan === tier.name;

          return (
            <div
              key={`${tier.name}-${index}`}
              className="group relative flex-1 p-2 rounded-3xl transition-all duration-100 border-4 border-white hover:box-content hover:border-[#BABAFB] hover:bg-[url('/images/pricing_bg.jpeg')] hover:bg-cover hover:bg-center"
            >
              <div
                className={`
                  relative rounded-2xl border overflow-hidden bg-[#F6F7F9] group-hover:bg-[#F6F7F9]/70  border-[#E6EAEF] transition-all duration-300 h-full
                  ${isCurrentPlan ? "border-purple-200" : ""}
                  group-hover:shadow-lg
                `}
              >
                <div className="flex items-start justify-between px-8 pt-8 bg-white">
                  <div className="">
                    <h3 className="text-xl font-bold text-[#101828]">
                      {tier.name}
                    </h3>
                    <p className="text-[#475467] text-sm">{tier.description}</p>
                  </div>
                  {isCurrentPlan && (
                    <div className="">
                      <div className="flex items-center gap-2 bg-gradient-to-b from-white to-[#F2EFFA] text-purple-700 px-3 py-2 border border-[#F1F1FE] rounded-full text-sm font-medium">
                        <Check size={16} />
                        Current Plan
                      </div>
                    </div>
                  )}
                </div>

                <div className=" py-8 px-8 bg-white">
                  <div className="flex items-center gap-2">
                    <span className="text-3xl font-bold text-black">
                      ${tier.price}
                    </span>
                    <span className="text-[#475467]">{tier.period}</span>
                  </div>
                </div>

                <div className="">
                  <div className="bg-transparent px-8 py-5 ">
                    <h4 className="text-gray-900 font-medium ">Includes:</h4>
                  </div>
                  <ul className="space-y-4 px-6 py-6 bg-white">
                    {tier.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          <Icons name="feature-bullets" svgProps={{}} />
                        </div>
                        <span className="text-gray-700 leading-relaxed text-sm">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-auto bg-[#F6F7F9] pt-8 px-6 pb-6 h-full">
                  {currentPlan !== "Free" && isCurrentPlan ? (
                    <Button
                      variant={"outline"}
                      className="w-fit py-6 px-6 bg-white border border-[#F81404] text-white rounded-xl font-medium transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-[#F81404] cursor-pointer"
                      onClick={() => {
                        // handleUpgrade(tier.name)
                        if (!alternateStorageCancellationModalOpen) {
                          setIsOffloadModalOpen(true);
                        } else {
                          setIsBillingCancellationModalOpen(true);
                        }
                        setAlternateStorageCancellationModalOpen(true);
                      }}
                      disabled={isLoading || loadingPlan !== null}
                    >
                      {isLoading ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Cancelling...</span>
                        </div>
                      ) : (
                        "Cancel Subscription"
                      )}
                    </Button>
                  ) : PLAN_HIERACHRY.indexOf(currentPlan) >
                    PLAN_HIERACHRY.indexOf(tier.name) ? (
                    <div>
                      <Button
                        className="w-fit py-6 px-6 bg-white border border-[#7141F8] hover:opacity-90 text-white rounded-xl font-medium transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-[#7141F8] cursor-pointer"
                        onClick={() => {
                          // handleUpgrade(tier.name)
                          if (!alternateStorageCancellationModalOpen) {
                            setIsOffloadModalOpen(true);
                          } else {
                            setIsBillingCancellationModalOpen(true);
                          }
                          setAlternateStorageCancellationModalOpen(true);
                        }}
                        disabled={isLoading || loadingPlan !== null}
                      >
                        {isLoading ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Downgrading...</span>
                          </div>
                        ) : (
                          `Downgrade to Telex ${tier.name}`
                        )}
                      </Button>
                    </div>
                  ) : (
                    tier.name !== "Free" && (
                      <Button
                        className="w-fit py-6 px-6 bg-gradient-to-b from-[#8860f8] to-[#7141f8] hover:opacity-90 text-white rounded-xl font-medium transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                        onClick={() => handleUpgrade(tier.name)}
                        disabled={isLoading || loadingPlan !== null}
                      >
                        {isLoading ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Upgrading...</span>
                          </div>
                        ) : (
                          tier.buttonText
                        )}
                      </Button>
                    )
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Page;
