import { AIUsageRecord, PricingTier, TelexTransaction } from "../type";

export const mockData: AIUsageRecord[] = [
  {
    id: "1",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "#content-ideas",
    type: "Channel",
    aiCredits: 5,
    dateTime: "29/04/2025 at 16:20",
    status: "online",
  },
  {
    id: "2",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "@<PERSON>",
    type: "Private Chat",
    aiCredits: 4,
    dateTime: "29/04/2025 at 15:05",
    status: "online",
  },
  {
    id: "3",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@<PERSON> Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "29/04/2025 at 14:47",
    status: "online",
  },
  {
    id: "4",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "#brand-marketing",
    type: "Channel",
    aiCredits: 5,
    dateTime: "28/04/2025 at 16:20",
    status: "online",
  },
  {
    id: "5",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "#july-campaign",
    type: "Channel",
    aiCredits: 5,
    dateTime: "28/04/2025 at 14:13",
    status: "online",
  },
  {
    id: "6",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "@Jennifer Garner",
    type: "Private Chat",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:12",
    status: "online",
  },
  {
    id: "7",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Jennifer Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:01",
    status: "online",
  },
  {
    id: "8",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Jennifer Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:01",
    status: "online",
  },
  {
    id: "9",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Jennifer Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:01",
    status: "online",
  },
  {
    id: "10",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Jennifer Garner",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "28/04/2025 at 12:01",
    status: "online",
  },
  {
    id: "11",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "#general",
    type: "Channel",
    aiCredits: 3,
    dateTime: "27/04/2025 at 18:45",
    status: "online",
  },
  {
    id: "12",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "@Michael Chen",
    type: "Private Chat",
    aiCredits: 7,
    dateTime: "27/04/2025 at 17:30",
    status: "online",
  },
  {
    id: "13",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "#tech-support",
    type: "Channel",
    aiCredits: 6,
    dateTime: "27/04/2025 at 16:15",
    status: "online",
  },
  {
    id: "14",
    agentName: "Pulse - Analytics Bot",
    agentType: "analytics",
    nameOwner: "@Sarah Wilson",
    type: "Private Group",
    aiCredits: 8,
    dateTime: "27/04/2025 at 15:22",
    status: "online",
  },
  {
    id: "15",
    agentName: "Zephyr - Scheduler",
    agentType: "scheduler",
    nameOwner: "#project-alpha",
    type: "Channel",
    aiCredits: 4,
    dateTime: "27/04/2025 at 14:10",
    status: "offline",
  },
  {
    id: "16",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "@David Rodriguez",
    type: "Private Chat",
    aiCredits: 2,
    dateTime: "27/04/2025 at 13:55",
    status: "online",
  },
  {
    id: "17",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "#international-team",
    type: "Channel",
    aiCredits: 9,
    dateTime: "27/04/2025 at 12:40",
    status: "online",
  },
  {
    id: "18",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Emma Thompson",
    type: "Private Group",
    aiCredits: 5,
    dateTime: "27/04/2025 at 11:25",
    status: "online",
  },
  {
    id: "19",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "@Alex Johnson",
    type: "Private Chat",
    aiCredits: 6,
    dateTime: "26/04/2025 at 19:30",
    status: "online",
  },
  {
    id: "20",
    agentName: "Pulse - Analytics Bot",
    agentType: "analytics",
    nameOwner: "#data-science",
    type: "Channel",
    aiCredits: 10,
    dateTime: "26/04/2025 at 18:15",
    status: "online",
  },
  {
    id: "21",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "#community",
    type: "Channel",
    aiCredits: 3,
    dateTime: "26/04/2025 at 17:45",
    status: "online",
  },
  {
    id: "22",
    agentName: "Zephyr - Scheduler",
    agentType: "scheduler",
    nameOwner: "@Lisa Park",
    type: "Private Group",
    aiCredits: 4,
    dateTime: "26/04/2025 at 16:20",
    status: "offline",
  },
  {
    id: "23",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "@Robert Kim",
    type: "Private Chat",
    aiCredits: 2,
    dateTime: "26/04/2025 at 15:10",
    status: "online",
  },
  {
    id: "24",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "#global-marketing",
    type: "Channel",
    aiCredits: 8,
    dateTime: "26/04/2025 at 14:35",
    status: "online",
  },
  {
    id: "25",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "@Maria Garcia",
    type: "Private Group",
    aiCredits: 7,
    dateTime: "26/04/2025 at 13:50",
    status: "online",
  },
  {
    id: "26",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "#development",
    type: "Channel",
    aiCredits: 5,
    dateTime: "26/04/2025 at 12:25",
    status: "online",
  },
  {
    id: "27",
    agentName: "Pulse - Analytics Bot",
    agentType: "analytics",
    nameOwner: "@James Wilson",
    type: "Private Chat",
    aiCredits: 9,
    dateTime: "26/04/2025 at 11:40",
    status: "online",
  },
  {
    id: "28",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "@Nina Patel",
    type: "Private Group",
    aiCredits: 3,
    dateTime: "25/04/2025 at 20:15",
    status: "online",
  },
  {
    id: "29",
    agentName: "Zephyr - Scheduler",
    agentType: "scheduler",
    nameOwner: "#product-team",
    type: "Channel",
    aiCredits: 4,
    dateTime: "25/04/2025 at 19:30",
    status: "offline",
  },
  {
    id: "30",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "@Tom Anderson",
    type: "Private Chat",
    aiCredits: 2,
    dateTime: "25/04/2025 at 18:45",
    status: "online",
  },
  {
    id: "31",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "#customer-support",
    type: "Channel",
    aiCredits: 6,
    dateTime: "25/04/2025 at 17:20",
    status: "online",
  },
  {
    id: "32",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "@Rachel Green",
    type: "Private Group",
    aiCredits: 8,
    dateTime: "25/04/2025 at 16:10",
    status: "online",
  },
  {
    id: "33",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Kevin Lee",
    type: "Private Chat",
    aiCredits: 5,
    dateTime: "25/04/2025 at 15:35",
    status: "online",
  },
  {
    id: "34",
    agentName: "Pulse - Analytics Bot",
    agentType: "analytics",
    nameOwner: "#finance",
    type: "Channel",
    aiCredits: 7,
    dateTime: "25/04/2025 at 14:50",
    status: "online",
  },
  {
    id: "35",
    agentName: "Darl - Profanity Filter",
    agentType: "profanity-filter",
    nameOwner: "@Sophie Turner",
    type: "Private Group",
    aiCredits: 3,
    dateTime: "25/04/2025 at 13:25",
    status: "online",
  },
  {
    id: "36",
    agentName: "Zephyr - Scheduler",
    agentType: "scheduler",
    nameOwner: "@Daniel Brown",
    type: "Private Chat",
    aiCredits: 4,
    dateTime: "25/04/2025 at 12:40",
    status: "offline",
  },
  {
    id: "37",
    agentName: "Nova - Content Moderator",
    agentType: "content-moderator",
    nameOwner: "#announcements",
    type: "Channel",
    aiCredits: 2,
    dateTime: "25/04/2025 at 11:15",
    status: "online",
  },
  {
    id: "38",
    agentName: "Echo - Translation Bot",
    agentType: "translator",
    nameOwner: "@Isabella Martinez",
    type: "Private Group",
    aiCredits: 9,
    dateTime: "24/04/2025 at 22:30",
    status: "online",
  },
  {
    id: "39",
    agentName: "Sage - Knowledge Assistant",
    agentType: "knowledge-base",
    nameOwner: "#research",
    type: "Channel",
    aiCredits: 6,
    dateTime: "24/04/2025 at 21:45",
    status: "online",
  },
  {
    id: "40",
    agentName: "Lynx - Error Handler",
    agentType: "error-handler",
    nameOwner: "@Christopher Davis",
    type: "Private Chat",
    aiCredits: 5,
    dateTime: "24/04/2025 at 20:20",
    status: "online",
  },
];

export const pricingTiers: PricingTier[] = [
  {
    name: "Free",
    description: "For small teams & startups.",
    price: 0,
    period: "per month",
    features: [
      "No user limit",
      "No monthly platform charge",
      "Access to all free agents",
      "Pay as you go fee per paid agent usage",
      "10GB all-time data storage",
    ],
    buttonText: "Current Plan",
    isCurrentPlan: true,
  },
  {
    name: "Starter",
    description: "For scaling businesses.",
    price: 10,
    period: "per month",
    features: [
      "No user limit",
      "120 AI Credits",
      "Access to all free agents",
      "Pay as you go fee per paid agent usage",
      "30GB all-time data storage",
    ],
    buttonText: "Upgrade to Telex Starter",
    isHighlighted: true,
  },
  {
    name: "Community",
    description: "For approved communities.",
    price: 100,
    period: "per month",
    features: [
      "No user limit",
      "1,200 AI Credits",
      "Access to all free agents",
      "Pay as you go fee per paid agent usage",
      "200GB all-time data storage",
    ],
    buttonText: "Upgrade to Telex Community",
  },
];

export const SampleBillingPaymentHistory: TelexTransaction[] = [
  {
    id: "tx_001",
    description: "Telex Community Plan",
    extraDescription: "for 120 AI Credits",
    servicePeriod: "May 31 - June 31 2025",
    date: "2025-06-01",
    amount: {
      base: 133.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 135.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1091",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_002",
    description: "Telex Starter Plan",
    extraDescription: "for 80 AI Credits",
    servicePeriod: "May 31 2025 - June 31 2025",
    date: "2025-05-31",
    amount: {
      base: 13.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 15.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1090",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_003",
    description: "Telex Pro Plan",
    extraDescription: "for 200 AI Credits",
    servicePeriod: "April 30 - May 30 2025",
    date: "2025-04-30",
    amount: {
      base: 46.5,
      vat: 2.75,
      vatRate: 6.25,
      total: 49.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1089",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_004",
    description: "Telex Community Plan",
    extraDescription: "for 120 AI Credits",
    servicePeriod: "March 31 - April 30 2025",
    date: "2025-03-31",
    amount: {
      base: 133.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 135.0,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1088",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_005",
    description: "Telex Starter Plan",
    extraDescription: "for 85 AI Credits",
    servicePeriod: "February 28 - March 31 2025",
    date: "2025-02-28",
    amount: {
      base: 13.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 15.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1087",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_006",
    description: "Telex Pro Plan",
    extraDescription: "for 250 AI Credits",
    servicePeriod: "January 31 - February 28 2025",
    date: "2025-01-31",
    amount: {
      base: 46.5,
      vat: 2.75,
      vatRate: 6.25,
      total: 49.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1086",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_007",
    description: "Telex Enterprise Plan",
    extraDescription: "for 500 AI Credits",
    servicePeriod: "December 31 2024 - January 31 2025",
    date: "2024-12-31",
    amount: {
      base: 188.5,
      vat: 11.75,
      vatRate: 6.25,
      total: 200.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1085",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_008",
    description: "Telex Community Plan",
    extraDescription: "for 120 AI Credits",
    servicePeriod: "November 30 - December 31 2024",
    date: "2024-11-30",
    amount: {
      base: 133.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 135.0,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1084",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_009",
    description: "Telex Starter Plan",
    extraDescription: "for 75 AI Credits",
    servicePeriod: "October 31 - November 30 2024",
    date: "2024-10-31",
    amount: {
      base: 13.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 15.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1083",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_010",
    description: "Telex Pro Plan",
    extraDescription: "for 300 AI Credits",
    servicePeriod: "September 30 - October 31 2024",
    date: "2024-09-30",
    amount: {
      base: 46.5,
      vat: 2.75,
      vatRate: 6.25,
      total: 49.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1082",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_011",
    description: "Telex Community Plan",
    extraDescription: "for 140 AI Credits",
    servicePeriod: "August 31 - September 30 2024",
    date: "2024-08-31",
    amount: {
      base: 133.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 135.0,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1081",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_012",
    description: "Telex Starter Plan",
    extraDescription: "for 90 AI Credits",
    servicePeriod: "July 31 - August 31 2024",
    date: "2024-07-31",
    amount: {
      base: 13.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 15.0,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1080",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_013",
    description: "Telex Enterprise Plan",
    extraDescription: "for 600 AI Credits",
    servicePeriod: "June 30 - July 31 2024",
    date: "2024-06-30",
    amount: {
      base: 188.5,
      vat: 11.75,
      vatRate: 6.25,
      total: 200.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1079",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_014",
    description: "Telex Pro Plan",
    extraDescription: "for 220 AI Credits",
    servicePeriod: "May 31 - June 30 2024",
    date: "2024-05-31",
    amount: {
      base: 46.5,
      vat: 2.75,
      vatRate: 6.25,
      total: 49.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1078",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_015",
    description: "Telex Community Plan",
    extraDescription: "for 110 AI Credits",
    servicePeriod: "April 30 - May 31 2024",
    date: "2024-04-30",
    amount: {
      base: 133.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 135.0,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1077",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_016",
    description: "Telex Starter Plan",
    extraDescription: "for 95 AI Credits",
    servicePeriod: "March 31 - April 30 2024",
    date: "2024-03-31",
    amount: {
      base: 13.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 15.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1076",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_017",
    description: "Telex Pro Plan",
    extraDescription: "for 180 AI Credits",
    servicePeriod: "February 29 - March 31 2024",
    date: "2024-02-29",
    amount: {
      base: 46.5,
      vat: 2.75,
      vatRate: 6.25,
      total: 49.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1075",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_018",
    description: "Telex Enterprise Plan",
    extraDescription: "for 450 AI Credits",
    servicePeriod: "January 31 - February 29 2024",
    date: "2024-01-31",
    amount: {
      base: 188.5,
      vat: 11.75,
      vatRate: 6.25,
      total: 200.25,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1074",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_019",
    description: "Telex Community Plan",
    extraDescription: "for 130 AI Credits",
    servicePeriod: "December 31 2023 - January 31 2024",
    date: "2023-12-31",
    amount: {
      base: 133.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 135.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1073",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_020",
    description: "Telex Starter Plan",
    extraDescription: "for 70 AI Credits",
    servicePeriod: "November 30 - December 31 2023",
    date: "2023-11-30",
    amount: {
      base: 13.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 15.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1072",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_021",
    description: "Telex Pro Plan",
    extraDescription: "for 280 AI Credits",
    servicePeriod: "October 31 - November 30 2023",
    date: "2023-10-31",
    amount: {
      base: 46.5,
      vat: 2.75,
      vatRate: 6.25,
      total: 49.25,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1071",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_022",
    description: "Telex Community Plan",
    extraDescription: "for 115 AI Credits",
    servicePeriod: "September 30 - October 31 2023",
    date: "2023-09-30",
    amount: {
      base: 133.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 135.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1070",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_023",
    description: "Telex Enterprise Plan",
    extraDescription: "for 550 AI Credits",
    servicePeriod: "August 31 - September 30 2023",
    date: "2023-08-31",
    amount: {
      base: 188.5,
      vat: 11.75,
      vatRate: 6.25,
      total: 200.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1069",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_024",
    description: "Telex Starter Plan",
    extraDescription: "for 65 AI Credits",
    servicePeriod: "July 31 - August 31 2023",
    date: "2023-07-31",
    amount: {
      base: 13.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 15.0,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1068",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_025",
    description: "Telex Pro Plan",
    extraDescription: "for 190 AI Credits",
    servicePeriod: "June 30 - July 31 2023",
    date: "2023-06-30",
    amount: {
      base: 46.5,
      vat: 2.75,
      vatRate: 6.25,
      total: 49.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1067",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_026",
    description: "Telex Community Plan",
    extraDescription: "for 125 AI Credits",
    servicePeriod: "May 31 - June 30 2023",
    date: "2023-05-31",
    amount: {
      base: 133.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 135.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1066",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_027",
    description: "Telex Enterprise Plan",
    extraDescription: "for 400 AI Credits",
    servicePeriod: "April 30 - May 31 2023",
    date: "2023-04-30",
    amount: {
      base: 188.5,
      vat: 11.75,
      vatRate: 6.25,
      total: 200.25,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1065",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_028",
    description: "Telex Starter Plan",
    extraDescription: "for 100 AI Credits",
    servicePeriod: "March 31 - April 30 2023",
    date: "2023-03-31",
    amount: {
      base: 13.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 15.0,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1064",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_029",
    description: "Telex Pro Plan",
    extraDescription: "for 240 AI Credits",
    servicePeriod: "February 28 - March 31 2023",
    date: "2023-02-28",
    amount: {
      base: 46.5,
      vat: 2.75,
      vatRate: 6.25,
      total: 49.25,
    },
    paymentMethod: {
      type: "visa",
      lastFour: "4242",
      processor: "stripe",
    },
    invoiceNumber: "INV-1063",
    status: "paid",
    currency: "USD",
  },
  {
    id: "tx_030",
    description: "Telex Community Plan",
    extraDescription: "for 135 AI Credits",
    servicePeriod: "January 31 - February 28 2023",
    date: "2023-01-31",
    amount: {
      base: 133.45,
      vat: 1.25,
      vatRate: 6.25,
      total: 135.0,
    },
    paymentMethod: {
      type: "mastercard",
      lastFour: "7418",
      processor: "stripe",
    },
    invoiceNumber: "INV-1062",
    status: "paid",
    currency: "USD",
  },
];
