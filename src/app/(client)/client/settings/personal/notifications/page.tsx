"use client";

import React, { useState } from "react";
import SettingsLabel from "../../components/settings-label";

type Preferences = {
  notificationType: "all" | "mentions" | "nothing";
  timeFrom: string;
  timeTo: string;
  infoMethod: "mobile" | "email";
};

const defaultPreferences: Preferences = {
  notificationType: "all",
  timeFrom: "00:00",
  timeTo: "00:00",
  infoMethod: "mobile",
};

const NotificationPage = () => {
  const [prefs, setPrefs] = useState<Preferences>(defaultPreferences);
  const [initialPrefs, setInitialPrefs] =
    useState<Preferences>(defaultPreferences);

  const hasChanges = JSON.stringify(prefs) !== JSON.stringify(initialPrefs);

  const handleRevert = () => {
    setPrefs(initialPrefs);
  };

  const handleSave = () => {
    // Simulate save (e.g., API call)
    setInitialPrefs(prefs);
    console.log("Saved Preferences:", prefs);
  };

  return (
    <div>
      <SettingsLabel />
      <div className="p-4">
        <div className="mb-6">
          <h1 className="text-base font-semibold">
            Your Notification Preferences
          </h1>
          <p className="text-sm text-[#344054]">
            Manage when and why you get notified.
          </p>
        </div>

        <div className="max-w-2xl">
          <label className="block font-semibold text-sm mb-2">
            Send notifications for:
          </label>
          <div className="space-y-3">
            {["all", "mentions", "nothing"].map((type) => (
              <label key={type} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="notificationType"
                  checked={prefs.notificationType === type}
                  onChange={() =>
                    setPrefs((prev) => ({
                      ...prev,
                      notificationType: type as Preferences["notificationType"],
                    }))
                  }
                />
                <span className="capitalize text-sm">
                  {type === "all" ? "All new messages" : type}
                </span>
              </label>
            ))}
          </div>
        </div>

        <hr className="bg-[#E6EAEF] border my-4" />

        <div className="max-w-2xl">
          <label className="block font-semibold text-sm mb-2">
            Receive notifications only within:
          </label>
          <div className="flex items-center space-x-4">
            <div className="flex flex-col items-start gap-1 text-sm">
              <span className="font-medium">From</span>
              <input
                type="time"
                value={prefs.timeFrom}
                onChange={(e) =>
                  setPrefs((prev) => ({ ...prev, timeFrom: e.target.value }))
                }
                className="border rounded p-3"
              />
            </div>
            <div className="flex flex-col items-start gap-1 text-sm">
              <span className="font-medium">To</span>
              <input
                type="time"
                value={prefs.timeTo}
                onChange={(e) =>
                  setPrefs((prev) => ({ ...prev, timeTo: e.target.value }))
                }
                className="border rounded p-3"
              />
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            <span className="font-medium text-black">Note:</span> Outside this
            time, notifications are paused.
          </p>
        </div>

        <hr className="bg-[#E6EAEF] border my-4" />

        <div className="mb-4 max-w-2xl">
          <label className="block font-semibold text-sm mb-2">
            To keep me informed:
          </label>
          <div className="space-y-2 text-sm">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="infoMethod"
                checked={prefs.infoMethod === "mobile"}
                onChange={() =>
                  setPrefs((prev) => ({ ...prev, infoMethod: "mobile" }))
                }
              />
              <span>Use different settings for mobile devices</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="infoMethod"
                checked={prefs.infoMethod === "email"}
                onChange={() =>
                  setPrefs((prev) => ({ ...prev, infoMethod: "email" }))
                }
              />
              <span>Receive notifications via email</span>
            </label>
          </div>
        </div>

        {hasChanges && (
          <div className="flex justify-end space-x-3 pt-4 max-w-xl">
            <button
              onClick={handleRevert}
              className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100"
            >
              Revert Changes
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 rounded-md bg-purple-600 text-white hover:bg-purple-700"
            >
              Save Changes
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationPage;
