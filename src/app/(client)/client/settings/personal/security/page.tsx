"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react";
import React, { useState } from "react";
import SettingsLabel from "../../components/settings-label";
import { securityDetails } from "./securityDetails";
import ChangePassword from "./components/change-password";
import { Button } from "~/components/ui/button";

function getPaginationRange(currentPage: number, totalPages: number) {
  const range = [];
  const delta = 2;

  const start = Math.max(2, currentPage - delta);
  const end = Math.min(totalPages - 1, currentPage + delta);

  range.push(1);
  if (start > 2) range.push("...");

  for (let i = start; i <= end; i++) {
    range.push(i);
  }

  if (end < totalPages - 1) range.push("...");
  if (totalPages > 1) range.push(totalPages);

  return range;
}

const SecurityPage = () => {
  const itemsPerPage = 7;
  const [currentPage, setCurrentPage] = useState(1);
  const [changePasswordDialog, setChangePasswordDialog] = useState(false);

  const paginatedUsage = securityDetails.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div>
      <SettingsLabel />
      <div className="p-4 flex items-center justify-between gap-4">
        <div className="">
          <h1 className="text-base font-semibold">Your Account Security</h1>
          <p className="text-sm text-[#344054]">
            Keeping your data secure by staying in-the-know
          </p>
        </div>
        <button
          className="border px-4 py-2 rounded-md text-sm"
          onClick={() => setChangePasswordDialog(true)}
        >
          Change Password
        </button>
      </div>

      <div className="m-4 border rounded-md">
        <div className="bg-[#f6f7f9] px-5 py-4 grid grid-cols-5 text-[#667085] rounded-t-md">
          <div className="">
            <p className="text-sm">Action</p>
          </div>
          <div className="">
            <p className="text-sm">Device</p>
          </div>
          <div className="flex items-center gap-1">
            <p className="text-sm">Location</p>
          </div>
          <div className="flex items-center gap-1">
            <p className="text-sm">Date</p>
            <ArrowDown className="text-[#98A2B3] w-4 h-4" />
          </div>
          <div className="flex items-center gap-1">
            <p className="text-sm">Last Active</p>
            <ArrowDown className="text-[#98A2B3] w-4 h-4" />
          </div>
        </div>

        {paginatedUsage.map((detail, index) => (
          <div
            key={index}
            className="bg-white px-5 py-5 grid grid-cols-5 border-t border-[#E6EAEF] text-[#344054] text-sm last:rounded-b-md"
          >
            <div>{detail.action}</div>
            <div>{detail.device}</div>
            <div>{detail.location}</div>
            <div>{detail.date}</div>
            <div>{detail.lastActive}</div>
          </div>
        ))}

        {Math.ceil(securityDetails.length / itemsPerPage) > 1 && (
          <div className="flex justify-between items-center gap-2 p-5 border-t border-[#E6EAEF]">
            <Button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              variant="outline"
            >
              <ArrowLeft className="text-[#667085] w-4 h-4" /> Previous
            </Button>

            <div className="">
              {getPaginationRange(
                currentPage,
                Math.ceil(securityDetails.length / itemsPerPage)
              ).map((page, idx) => (
                <button
                  key={idx}
                  className={`px-3 py-1 rounded ${
                    page === currentPage
                      ? "bg-[#E6EAEF] font-medium"
                      : "hover:bg-gray-100"
                  } ${page === "..." ? "cursor-default" : "cursor-pointer"}`}
                  onClick={() =>
                    typeof page === "number" && setCurrentPage(page)
                  }
                  disabled={page === "..."}
                >
                  {page}
                </button>
              ))}
            </div>

            <Button
              onClick={() =>
                setCurrentPage((prev) =>
                  prev < Math.ceil(securityDetails.length / itemsPerPage)
                    ? prev + 1
                    : prev
                )
              }
              disabled={
                currentPage === Math.ceil(securityDetails.length / itemsPerPage)
              }
              variant="outline"
              className="flex items-center gap-1"
            >
              Next <ArrowRight className="text-[#667085] w-4 h-4" />
            </Button>
          </div>
        )}
      </div>

      {changePasswordDialog && (
        <div className="absolute left-0 right-0 top-0 h-full bg-transparent z-20 flex items-center justify-center py-10 backdrop-blur-sm bg-red-500 ">
          <ChangePassword setChangePasswordDialog={setChangePasswordDialog} />
        </div>
      )}
    </div>
  );
};

export default SecurityPage;
