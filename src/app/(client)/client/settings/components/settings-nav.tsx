"use client";
import { useContext, useEffect, useRef, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { usePathname, useRouter } from "next/navigation";
import {
  XIcon,
  UserIcon,
  BellIcon,
  LockIcon,
  SettingsIcon,
  UsersIcon,
  BanknoteIcon,
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { DropdownIcon, PencilIcon } from "~/svgs";
import Link from "next/link";
import OrganisationMenu from "../../_components/org-dropdown";

export default function SettingsNav() {
  const { state, dispatch } = useContext(DataContext);
  const [openAccordions, setOpenAccordions] = useState<string[]>([]);
  const pathname = usePathname();
  const sidebarRef = useRef<HTMLDivElement | null>(null);
  const router = useRouter();

  useEffect(() => {
    const savedScroll = sessionStorage.getItem("sidebar-scroll");
    if (sidebarRef.current && savedScroll) {
      sidebarRef.current.scrollTop = parseInt(savedScroll, 10);
    }

    // Load previously open accordions
    const savedAccordions = localStorage.getItem("openAccordions");
    if (savedAccordions) {
      setOpenAccordions(JSON.parse(savedAccordions));
    }
  }, []);

  const handleAccordionChanges = (values: string[]) => {
    setOpenAccordions(values);
    localStorage.setItem("openAccordions", JSON.stringify(values));
  };

  const settings = [
    {
      id: "personal",
      trigger: "Personal",
      content: [
        {
          title: "Account",
          icon: <UserIcon className="w-5" />,
          link: "/client/settings/personal/account",
        },
        {
          title: "Notifications",
          icon: <BellIcon className="w-5" />,
          link: "/client/settings/personal/notifications",
        },
        {
          title: "Security",
          icon: <LockIcon className="w-5" />,
          link: "/client/settings/personal/security",
        },
      ],
    },
    {
      id: "organisation",
      trigger: "Organisation",
      content: [
        {
          title: "General",
          icon: <SettingsIcon className="w-5" />,
          link: "/client/settings/organisation/general",
        },
        {
          title: "User Management",
          icon: <UsersIcon className="w-5" />,
          link: "/client/settings/organisation/user-management",
        },
        {
          title: "Roles & Permissions",
          icon: <UserIcon className="w-5" />,
          link: "/client/settings/organisation/roles-permissions",
        },
        {
          title: "Billing",
          icon: <BanknoteIcon className="w-5" />,
          link: "/client/settings/organisation/billing",
        },
      ],
    },
  ];

  const handleNewChat = () => {
    dispatch({ type: ACTIONS.CLEAR_CHATS });
    router.push("/client/home/<USER>");
  };

  //

  return (
    <div
      className={`fixed top-[60px] left-[145px] h-[calc(100vh-60px)] bg-blue-300 md:translate-x-0 ${
        state?.channelBar ? "translate-x-0" : "-translate-x-full"
      } pt-6 flex flex-col gap-6 min-w-[300px] transition-transform duration-300 ease-in-out z-20 md:z-0`}
    >
      <div className="flex items-center justify-between px-4">
        <div className="flex items-center gap-[5px] md:justify-between w-full">
          <div className="flex items-center gap-[5px] md:justify-between w-full">
            <OrganisationMenu />

            <button type="button" onClick={handleNewChat}>
              <PencilIcon />
            </button>
          </div>
        </div>

        <XIcon
          className="block md:hidden text-gray-500 cursor-pointer"
          onClick={() =>
            dispatch({ type: ACTIONS.CHANNEL_BAR, payload: false })
          }
        />
      </div>

      <div
        className="overflow-auto text-blue-50 cursor-pointer px-4"
        ref={sidebarRef}
      >
        <Accordion
          type="multiple"
          className="w-full"
          value={openAccordions}
          onValueChange={handleAccordionChanges}
        >
          {settings.map((setting) => (
            <AccordionItem
              key={setting.id}
              value={setting.id}
              className="border-none"
            >
              <AccordionTrigger className="font-normal w-full py-0">
                <div className="relative py-3 mx-2 flex items-center gap-1 rounded-lg cursor-pointer w-full">
                  <DropdownIcon
                    className={`transition-transform duration-300 ${
                      openAccordions.includes(setting.id)
                        ? "rotate-0"
                        : "-rotate-90"
                    }`}
                  />
                  <h3 className="text-sm font-medium">{setting.trigger}</h3>
                </div>
              </AccordionTrigger>

              <AccordionContent>
                <ul className="flex flex-col gap-1">
                  {setting.content.map((item, index) => (
                    <li
                      key={index}
                      className={`flex items-center gap-2 px-2 py-1 rounded-lg ${pathname === item.link ? "bg-blue-200" : "hover:bg-blue-200"}`}
                    >
                      <div className="text-blue-50">{item.icon}</div>
                      <Link
                        href={item.link}
                        className={`text-sm leading-4 truncate w-full text-blue-50 ${pathname === item.link ? "text-white" : ""} `}
                      >
                        {item.title}
                      </Link>
                    </li>
                  ))}
                </ul>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
}
