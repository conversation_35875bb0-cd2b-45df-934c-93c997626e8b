<svg width="278" height="20" viewBox="0 0 278 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_32366_33060)">
<rect width="278" height="20" fill="#F9FAFB"/>
<line x1="-317.707" y1="323.293" x2="844.621" y2="-839.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="333.293" x2="844.621" y2="-829.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="343.293" x2="844.621" y2="-819.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="353.293" x2="844.621" y2="-809.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="363.293" x2="844.621" y2="-799.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="373.293" x2="844.621" y2="-789.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="383.293" x2="844.621" y2="-779.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="393.293" x2="844.621" y2="-769.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="403.293" x2="844.621" y2="-759.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="413.293" x2="844.621" y2="-749.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="423.293" x2="844.621" y2="-739.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="433.293" x2="844.621" y2="-729.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="443.293" x2="844.621" y2="-719.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="453.293" x2="844.621" y2="-709.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="463.293" x2="844.621" y2="-699.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="473.293" x2="844.621" y2="-689.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="483.293" x2="844.621" y2="-679.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="493.293" x2="844.621" y2="-669.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="503.293" x2="844.621" y2="-659.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="513.293" x2="844.621" y2="-649.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="523.293" x2="844.621" y2="-639.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="533.293" x2="844.621" y2="-629.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="543.293" x2="844.621" y2="-619.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="553.293" x2="844.621" y2="-609.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="563.293" x2="844.621" y2="-599.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="573.293" x2="844.621" y2="-589.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="583.293" x2="844.621" y2="-579.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="593.293" x2="844.621" y2="-569.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="603.293" x2="844.621" y2="-559.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="613.293" x2="844.621" y2="-549.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="623.293" x2="844.621" y2="-539.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="633.293" x2="844.621" y2="-529.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="643.293" x2="844.621" y2="-519.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="653.293" x2="844.621" y2="-509.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="663.293" x2="844.621" y2="-499.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="673.293" x2="844.621" y2="-489.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="683.293" x2="844.621" y2="-479.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="693.293" x2="844.621" y2="-469.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="703.293" x2="844.621" y2="-459.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="713.293" x2="844.621" y2="-449.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="723.293" x2="844.621" y2="-439.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="733.293" x2="844.621" y2="-429.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="743.293" x2="844.621" y2="-419.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="753.293" x2="844.621" y2="-409.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="763.293" x2="844.621" y2="-399.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="773.293" x2="844.621" y2="-389.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="783.293" x2="844.621" y2="-379.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="793.293" x2="844.621" y2="-369.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="803.293" x2="844.621" y2="-359.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="813.293" x2="844.621" y2="-349.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="823.293" x2="844.621" y2="-339.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="833.293" x2="844.621" y2="-329.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="843.293" x2="844.621" y2="-319.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="853.293" x2="844.621" y2="-309.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="863.293" x2="844.621" y2="-299.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="873.293" x2="844.621" y2="-289.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="883.293" x2="844.621" y2="-279.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="893.293" x2="844.621" y2="-269.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="903.293" x2="844.621" y2="-259.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="913.293" x2="844.621" y2="-249.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="923.293" x2="844.621" y2="-239.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="933.293" x2="844.621" y2="-229.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="943.293" x2="844.621" y2="-219.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="953.293" x2="844.621" y2="-209.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="963.293" x2="844.621" y2="-199.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="973.293" x2="844.621" y2="-189.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="983.293" x2="844.621" y2="-179.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="993.293" x2="844.621" y2="-169.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="1003.29" x2="844.621" y2="-159.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="1013.29" x2="844.621" y2="-149.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="1023.29" x2="844.621" y2="-139.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="1033.29" x2="844.621" y2="-129.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="1043.29" x2="844.621" y2="-119.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="1053.29" x2="844.621" y2="-109.035" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="1063.29" x2="844.621" y2="-99.0351" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="1073.29" x2="844.621" y2="-89.0351" stroke="#D31103" stroke-width="2"/>
<line x1="-317.707" y1="1083.29" x2="844.621" y2="-79.0351" stroke="#D31103" stroke-width="2"/>
</g>
<defs>
<clipPath id="clip0_32366_33060">
<rect width="278" height="20" fill="white"/>
</clipPath>
</defs>
</svg>
